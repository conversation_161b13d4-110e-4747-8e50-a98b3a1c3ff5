import { NextRequest, NextResponse } from 'next/server';

// 数据表映射
const TABLE_MAPPING = {
  'shift_samples': '生产日报-FDX',
  'filter_samples': '压滤样化验记录',
  'incoming_samples': '进厂原矿-FDX',
  'outgoing_sample': '出厂精矿-FDX'
};

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { sampleType, id, data } = body;

    console.log('Lab数据更新请求:', { sampleType, id, data });

    if (!sampleType || !id || !data) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      );
    }

    const tableName = TABLE_MAPPING[sampleType as keyof typeof TABLE_MAPPING];
    if (!tableName) {
      return NextResponse.json(
        { success: false, error: '无效的数据源类型' },
        { status: 400 }
      );
    }

    // 获取环境变量
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !anonKey) {
      console.error('Supabase 配置缺失');
      return NextResponse.json(
        { success: false, error: 'Supabase 配置缺失' },
        { status: 500 }
      );
    }

    // 准备更新数据，添加更新时间
    const updateData = {
      ...data,
      updated_at: new Date().toISOString()
    };

    // 移除不应该更新的字段
    delete updateData.id;
    delete updateData.created_at;

    // 移除前端显示用的虚拟字段（这些字段在数据库中不存在）
    delete updateData['元素'];
    delete updateData['品位'];
    delete updateData['水分'];
    delete updateData['矿物类型'];
    delete updateData['湿重'];
    delete updateData['干重'];
    delete updateData['氧化率'];
    delete updateData['金属量'];
    delete updateData['精矿数量'];
    delete updateData['精矿品位'];
    delete updateData['精矿金属量'];
    delete updateData['尾矿数量'];
    delete updateData['尾矿品位'];
    delete updateData['尾矿金属量'];
    delete updateData['理论回收率'];
    delete updateData['实际回收率'];
    delete updateData['回收率差异'];
    delete updateData['处理量'];
    delete updateData['作业率'];
    delete updateData['设备状态'];
    delete updateData['备注'];

    console.log('准备更新的数据:', updateData);

    // 处理合成ID（如 "8-pb" -> "8"）
    const actualId = id.includes('-') ? id.split('-')[0] : id;
    console.log('原始ID:', id, '实际数据库ID:', actualId);

    // 构建Supabase更新URL
    const updateUrl = `${supabaseUrl}/rest/v1/${encodeURIComponent(tableName)}?id=eq.${encodeURIComponent(actualId)}`;

    const response = await fetch(updateUrl, {
      method: 'PATCH',
      headers: {
        'apikey': anonKey,
        'Authorization': `Bearer ${anonKey}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(updateData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Supabase 更新错误:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
      return NextResponse.json(
        { success: false, error: `数据库更新失败: ${response.statusText}` },
        { status: response.status }
      );
    }

    const updatedData = await response.json();
    console.log('✅ 数据更新成功:', updatedData);

    return NextResponse.json({
      success: true,
      data: updatedData,
      message: '数据更新成功'
    });

  } catch (error) {
    console.error('Lab数据更新错误:', {
      message: error instanceof Error ? error.message : '未知错误',
      details: error instanceof Error ? error.stack : error
    });

    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '服务器内部错误' 
      },
      { status: 500 }
    );
  }
}
