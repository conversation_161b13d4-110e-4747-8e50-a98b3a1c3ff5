import { NextRequest, NextResponse } from 'next/server';

// 数据表映射
const TABLE_MAPPING = {
  'shift_samples': '生产日报-FDX',
  'filter_samples': '压滤样化验记录',
  'incoming_samples': '进厂原矿-FDX',
  'outgoing_sample': '出厂精矿-FDX'
};

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { sampleType, id, data } = body;

    console.log('Lab数据更新请求:', { sampleType, id, data });

    if (!sampleType || !id || !data) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      );
    }

    const tableName = TABLE_MAPPING[sampleType as keyof typeof TABLE_MAPPING];
    if (!tableName) {
      return NextResponse.json(
        { success: false, error: '无效的数据源类型' },
        { status: 400 }
      );
    }

    // 获取环境变量
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !anonKey) {
      console.error('Supabase 配置缺失');
      return NextResponse.json(
        { success: false, error: 'Supabase 配置缺失' },
        { status: 500 }
      );
    }

    // 准备更新数据，添加更新时间
    const updateData = {
      ...data,
      updated_at: new Date().toISOString()
    };

    // 移除不应该更新的字段
    delete updateData.id;
    delete updateData.created_at;

    console.log('准备更新的数据:', updateData);

    // 构建Supabase更新URL
    const updateUrl = `${supabaseUrl}/rest/v1/${encodeURIComponent(tableName)}?id=eq.${encodeURIComponent(id)}`;

    const response = await fetch(updateUrl, {
      method: 'PATCH',
      headers: {
        'apikey': anonKey,
        'Authorization': `Bearer ${anonKey}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(updateData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Supabase 更新错误:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
      return NextResponse.json(
        { success: false, error: `数据库更新失败: ${response.statusText}` },
        { status: response.status }
      );
    }

    const updatedData = await response.json();
    console.log('✅ 数据更新成功:', updatedData);

    return NextResponse.json({
      success: true,
      data: updatedData,
      message: '数据更新成功'
    });

  } catch (error) {
    console.error('Lab数据更新错误:', {
      message: error instanceof Error ? error.message : '未知错误',
      details: error instanceof Error ? error.stack : error
    });

    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '服务器内部错误' 
      },
      { status: 500 }
    );
  }
}
