"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { AuthGuard } from "@/components/auth-guard";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Footer } from "@/components/ui/footer";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { HamburgerMenu } from "@/components/hamburger-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Beaker,
  Clock,
  Filter,
  Truck,
  FlaskConical,
  Search,
  RefreshCw,
  Moon,
  Sun,
  Calendar,
  BarChart3,
  TrendingUp,
  Edit,
  Save,
  X
} from "lucide-react";
import { useTheme } from "next-themes";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartConfig } from "@/components/ui/chart";
import { LineChart, Line, XAxis, YAxis, CartesianGrid } from "recharts";

// 简化的数据类型定义
interface SampleData {
  id: string;
  record_date?: string;
  shipment_date?: string;
  shift?: string;
  mineral_type?: string;
  element?: string;
  grade_value?: number | null;
  moisture_value?: number | null;
  filter_press_number?: string;
  operator?: string; // 操作员字段
  supplier?: string;
  purchasing_unit_name?: string;
  assayed_metal_element?: string;
  sample_number?: string; // 样品编号字段
  shipment_sample_grade_percentage?: number | null;
  shipment_sample_moisture_percentage?: number | null;
  created_at?: string;
  updated_at?: string;
}

type DataSource = 'shift_samples' | 'filter_samples' | 'incoming_samples' | 'outgoing_sample';

interface LabDateRange {
  from: Date;
  to: Date;
}

// 图表配置
const chartConfig = {
  jinding_grade: {
    label: "金鼎品位",
    color: "var(--chart-1)",
  },
  fudingxiang_grade: {
    label: "富鼎翔品位",
    color: "var(--chart-2)",
  },
  jinding_moisture: {
    label: "金鼎水分",
    color: "var(--chart-3)",
  },
  fudingxiang_moisture: {
    label: "富鼎翔水分",
    color: "var(--chart-4)",
  },
  jinding_weight: {
    label: "金鼎湿重",
    color: "var(--chart-1)",
  },
  fudingxiang_weight: {
    label: "富鼎翔湿重",
    color: "var(--chart-2)",
  },
  jinding_metal: {
    label: "金鼎金属量",
    color: "var(--chart-3)",
  },
  fudingxiang_metal: {
    label: "富鼎翔金属量",
    color: "var(--chart-4)",
  },
  moisture: {
    label: "原矿水份",
    color: "var(--chart-1)",
  },
  zn_grade: {
    label: "Zn品位",
    color: "var(--chart-2)",
  },
  pb_grade: {
    label: "Pb品位",
    color: "var(--chart-3)",
  },
  zn_recovery: {
    label: "Zn回收率",
    color: "var(--chart-1)",
  },
  pb_recovery: {
    label: "Pb回收率",
    color: "var(--chart-2)",
  },
} satisfies ChartConfig

// 模拟数据生成函数
const generateMockData = () => {
  const dates = Array.from({ length: 30 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - (29 - i));
    return date.toISOString().split('T')[0];
  });

  return {
    // 进厂数据
    incoming: {
      gradeAndMoisture: dates.map(date => ({
        date,
        jinding_grade: (Math.random() * 5 + 15).toFixed(2),
        fudingxiang_grade: (Math.random() * 5 + 16).toFixed(2),
        jinding_moisture: (Math.random() * 3 + 8).toFixed(2),
        fudingxiang_moisture: (Math.random() * 3 + 7).toFixed(2),
      })),
      weightAndMetal: dates.map(date => ({
        date,
        jinding_weight: (Math.random() * 50 + 200).toFixed(1),
        fudingxiang_weight: (Math.random() * 50 + 220).toFixed(1),
        jinding_metal: (Math.random() * 20 + 30).toFixed(1),
        fudingxiang_metal: (Math.random() * 20 + 35).toFixed(1),
      })),
    },
    // 生产数据
    production: {
      originalOre: dates.map(date => ({
        date,
        moisture: (Math.random() * 2 + 8).toFixed(2),
        zn_grade: (Math.random() * 3 + 12).toFixed(2),
        pb_grade: (Math.random() * 2 + 3).toFixed(2),
      })),
      concentrate: dates.map(date => ({
        date,
        zn_grade: (Math.random() * 5 + 50).toFixed(2),
        pb_grade: (Math.random() * 5 + 60).toFixed(2),
      })),
      tailings: dates.map(date => ({
        date,
        zn_grade: (Math.random() * 1 + 1).toFixed(2),
        pb_grade: (Math.random() * 0.5 + 0.5).toFixed(2),
      })),
      recovery: dates.map(date => ({
        date,
        zn_recovery: (Math.random() * 5 + 85).toFixed(2),
        pb_recovery: (Math.random() * 5 + 88).toFixed(2),
      })),
    },
    // 出厂数据
    outgoing: {
      gradeAndMoisture: dates.map(date => ({
        date,
        jinding_grade: (Math.random() * 3 + 52).toFixed(2),
        fudingxiang_grade: (Math.random() * 3 + 53).toFixed(2),
        jinding_moisture: (Math.random() * 2 + 6).toFixed(2),
        fudingxiang_moisture: (Math.random() * 2 + 5).toFixed(2),
      })),
      weightAndMetal: dates.map(date => ({
        date,
        jinding_weight: (Math.random() * 30 + 100).toFixed(1),
        fudingxiang_weight: (Math.random() * 30 + 110).toFixed(1),
        jinding_metal: (Math.random() * 15 + 50).toFixed(1),
        fudingxiang_metal: (Math.random() * 15 + 55).toFixed(1),
      })),
    },
  };
};

// 主题切换组件
function ThemeToggle() {
  const { setTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">切换主题</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          浅色
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          深色
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          系统
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// 图表组件
function ComparisonChart({
  data,
  title,
  description,
  lines,
  trendText = "数据趋势稳定"
}: {
  data: any[],
  title: string,
  description: string,
  lines: { dataKey: string }[],
  trendText?: string
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <LineChart
            accessibilityLayer
            data={data}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
            />
            <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
            {lines.map((line, index) => (
              <Line
                key={line.dataKey}
                dataKey={line.dataKey}
                type="monotone"
                stroke={`var(--color-${line.dataKey})`}
                strokeWidth={2}
                dot={false}
              />
            ))}
          </LineChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              {trendText} <TrendingUp className="h-4 w-4" />
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              显示最近30天的数据趋势
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}

function LabPageContent() {
  const router = useRouter();

  // 状态管理
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [tableData, setTableData] = useState<SampleData[]>([]);
  const [selectedDataSource, setSelectedDataSource] = useState<DataSource>('shift_samples');

  // 详情对话框状态
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState<SampleData | null>(null);
  const [rawRowData, setRawRowData] = useState<any>(null);

  // 编辑模式状态
  const [isEditMode, setIsEditMode] = useState(false);
  const [editFormData, setEditFormData] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);

  // 图表数据状态
  const [chartData, setChartData] = useState(() => generateMockData());

  // 日期选择状态
  const [startDate, setStartDate] = useState<Date | undefined>(() => {
    const date = new Date();
    date.setDate(date.getDate() - 7); // 默认最近一周
    return date;
  });
  const [endDate, setEndDate] = useState<Date | undefined>(() => new Date());

  // 快速日期选择功能
  const setQuickDateRange = useCallback((days: number) => {
    const end = new Date();
    const start = new Date();
    start.setDate(end.getDate() - days);
    setStartDate(start);
    setEndDate(end);
  }, []);

  // 数据源标签映射
  const dataSourceLabel = {
    'shift_samples': '班样',
    'filter_samples': '压滤样',
    'incoming_samples': '进厂样',
    'outgoing_sample': '出厂样'
  };

  // 专项作业区配置
  const workAreas = [
    {
      icon: Clock,
      label: "班样",
      description: "班次样品化验",
      dataSource: 'shift_samples' as DataSource,
      isNavigationButton: true,
      route: '/shift-sample'
    },
    {
      icon: Filter,
      label: "压滤样",
      description: "压滤机样品化验",
      dataSource: 'filter_samples' as DataSource,
      isNavigationButton: true,
      route: '/filter-sample'
    },
    {
      icon: Beaker,
      label: "进厂样",
      description: "进厂原矿化验",
      dataSource: 'incoming_samples' as DataSource,
      isNavigationButton: true,
      route: '/incoming-sample'
    },
    {
      icon: Truck,
      label: "出厂样",
      description: "出厂精矿化验",
      dataSource: 'outgoing_sample' as DataSource,
      isNavigationButton: true,
      route: '/outgoing-sample'
    }
  ];

  // 数据获取函数
  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      // 构建API URL参数
      const params = new URLSearchParams({
        sampleType: selectedDataSource,
        limit: '50'
      });

      // 添加日期范围参数
      if (startDate) {
        params.append('startDate', startDate.toISOString().split('T')[0]);
      }
      if (endDate) {
        params.append('endDate', endDate.toISOString().split('T')[0]);
      }

      const response = await fetch(`/api/lab-data?${params.toString()}`);
      const result = await response.json();

      if (result.success) {
        // 转换API数据格式以匹配组件期望的格式
        const transformedData: SampleData[] = result.data.map((item: any) => ({
          id: item.id,
          record_date: item.日期 || item.出厂日期,
          shipment_date: item.出厂日期,
          element: item.元素 || item.化验元素,
          grade_value: parseFloat(item.品位 || item.出厂样品位) || 0,
          moisture_value: parseFloat(item.水分 || item.出厂样水分) || 0,
          shift: item.班次,
          mineral_type: item.矿物类型,
          supplier: item.供应商,
          purchasing_unit_name: item.采购单位,
          filter_press_number: item.压滤机编号,
          operator: item.操作员, // 添加操作员字段映射
          sample_number: item.样品编号, // 添加样品编号字段映射
          assayed_metal_element: item.元素,
          shipment_sample_grade_percentage: parseFloat(item.出厂样品位) || null,
          shipment_sample_moisture_percentage: parseFloat(item.出厂样水分) || null,
          created_at: item.created_at,
          updated_at: item.updated_at
        }));

        setTableData(transformedData);
        console.log(`成功获取 ${transformedData.length} 条 ${dataSourceLabel[selectedDataSource]} 数据`);
      } else {
        console.error('API 错误:', result.error);
        setTableData([]);
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      setTableData([]);
    } finally {
      setIsLoading(false);
    }
  }, [selectedDataSource, startDate, endDate]);

  // 数据源切换
  const handleDataSourceChange = useCallback(async (source: DataSource) => {
    setSelectedDataSource(source);
  }, []);

  // 处理专项作业区点击
  const handleWorkAreaClick = useCallback((area: typeof workAreas[0]) => {
    if (area.isNavigationButton && area.route) {
      router.push(area.route);
    } else {
      handleDataSourceChange(area.dataSource);
    }
  }, [router, handleDataSourceChange]);

  // 格式化日期显示
  const formatDate = useCallback((dateString: string | null | undefined) => {
    if (!dateString) return '--';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return dateString;
    }
  }, []);

  // 格式化数值显示
  const formatValue = useCallback((value: any, precision: number = 2) => {
    if (value === null || value === undefined || value === '') return '--';
    if (typeof value === 'number') return value.toFixed(precision);
    return value.toString();
  }, []);

  // 处理表单字段变化
  const handleFieldChange = useCallback((fieldKey: string, value: any) => {
    setEditFormData((prev: any) => ({
      ...prev,
      [fieldKey]: value
    }));
  }, []);

  // 渲染详情对话框内容
  const renderDetailContent = useCallback(() => {
    if (!rawRowData || !selectedRowData) return null;

    const dataSourceTitles = {
      'shift_samples': '班样数据详情',
      'filter_samples': '压滤样数据详情',
      'incoming_samples': '进厂样数据详情',
      'outgoing_sample': '出厂样数据详情'
    };

    const dataSourceTables = {
      'shift_samples': '生产日报-FDX',
      'filter_samples': '压滤样化验记录',
      'incoming_samples': '进厂原矿-FDX',
      'outgoing_sample': '出厂精矿-FDX'
    };

    // 使用编辑数据或原始数据
    const displayData = isEditMode ? editFormData : rawRowData;

    // 根据数据源类型定义字段显示顺序（与数据库字段顺序一致）
    const getFieldsForDataSource = () => {
      switch (selectedDataSource) {
        case 'shift_samples':
          return [
            { key: 'id', label: '记录ID', type: 'text' },
            { key: '日期', label: '日期', type: 'date' },
            { key: '班次', label: '班次', type: 'text' },
            { key: '氧化锌原矿-湿重（t）', label: '氧化锌原矿-湿重（t）', type: 'number' },
            { key: '氧化锌原矿-水份（%）', label: '氧化锌原矿-水份（%）', type: 'number' },
            { key: '氧化锌原矿-干重（t）', label: '氧化锌原矿-干重（t）', type: 'number' },
            { key: '氧化锌原矿-Pb全品位（%）', label: '氧化锌原矿-Pb全品位（%）', type: 'number' },
            { key: '氧化锌原矿-Zn全品位（%）', label: '氧化锌原矿-Zn全品位（%）', type: 'number' },
            { key: '氧化锌原矿-Pb氧化率（%）', label: '氧化锌原矿-Pb氧化率（%）', type: 'number' },
            { key: '氧化锌原矿-Zn氧化率（%）', label: '氧化锌原矿-Zn氧化率（%）', type: 'number' },
            { key: '氧化锌原矿-全金属（t）', label: '氧化锌原矿-全金属（t）', type: 'number' },
            { key: '氧化锌精矿-数量（t）', label: '氧化锌精矿-数量（t）', type: 'number' },
            { key: '氧化锌精矿-Pb品位（%）', label: '氧化锌精矿-Pb品位（%）', type: 'number' },
            { key: '氧化锌精矿-Zn品位（%）', label: '氧化锌精矿-Zn品位（%）', type: 'number' },
            { key: '氧化锌精矿-Pb金属量（t）', label: '氧化锌精矿-Pb金属量（t）', type: 'number' },
            { key: '氧化锌精矿-Zn金属量（t）', label: '氧化锌精矿-Zn金属量（t）', type: 'number' },
            { key: '尾矿-数量（t）', label: '尾矿-数量（t）', type: 'number' },
            { key: '尾矿-Pb全品位（%）', label: '尾矿-Pb全品位（%）', type: 'number' },
            { key: '尾矿-Zn全品位（%）', label: '尾矿-Zn全品位（%）', type: 'number' },
            { key: '尾矿-Pb全金属（t）', label: '尾矿-Pb全金属（t）', type: 'number' },
            { key: '尾矿-Zn全金属（t）', label: '尾矿-Zn全金属（t）', type: 'number' },
            { key: '氧化矿Zn理论回收率（%）', label: '氧化矿Zn理论回收率（%）', type: 'number' },
            { key: 'created_at', label: '创建时间', type: 'datetime' },
            { key: 'updated_at', label: '更新时间', type: 'datetime' }
          ];
        case 'filter_samples':
          return [
            { key: 'id', label: '记录ID', type: 'text' },
            { key: '操作员', label: '操作员', type: 'text' },
            { key: '开始时间', label: '开始时间', type: 'datetime' },
            { key: '结束时间', label: '结束时间', type: 'datetime' },
            { key: '水份', label: '水份(%)', type: 'number' },
            { key: '铅品位', label: '铅品位(%)', type: 'number' },
            { key: '锌品位', label: '锌品位(%)', type: 'number' },
            { key: '备注', label: '备注', type: 'text' },
            { key: 'created_at', label: '创建时间', type: 'datetime' },
            { key: 'updated_at', label: '更新时间', type: 'datetime' }
          ];
        case 'incoming_samples':
          return [
            { key: 'id', label: '记录ID', type: 'text' },
            { key: '化验人员', label: '化验人员', type: 'text' },
            { key: '原矿类型', label: '原矿类型', type: 'text' },
            { key: '计量日期', label: '计量日期', type: 'date' },
            { key: '湿重(t)', label: '湿重(t)', type: 'number' },
            { key: '水份(%)', label: '水份(%)', type: 'number' },
            { key: '干重(t)', label: '干重(t)', type: 'number' },
            { key: 'Pb', label: 'Pb品位(%)', type: 'number' },
            { key: 'Zn', label: 'Zn品位(%)', type: 'number' },
            { key: 'Zn氧化率', label: 'Zn氧化率(%)', type: 'number' },
            { key: 'Pb^M', label: 'Pb金属量(t)', type: 'number' },
            { key: 'Zn^M', label: 'Zn金属量(t)', type: 'number' },
            { key: 'Zn氧化率^M', label: 'Zn氧化率^M(%)', type: 'number' },
            { key: '发货单位名称', label: '发货单位名称', type: 'text' },
            { key: '收货单位名称', label: '收货单位名称', type: 'text' },
            { key: 'created_at', label: '创建时间', type: 'datetime' },
            { key: 'updated_at', label: '更新时间', type: 'datetime' }
          ];
        case 'outgoing_sample':
          return [
            { key: 'id', label: '记录ID', type: 'text' },
            { key: '化验人员', label: '化验人员', type: 'text' },
            { key: '样品编号', label: '样品编号', type: 'text' },
            { key: '计量日期', label: '计量日期', type: 'date' },
            { key: '车牌号', label: '车牌号', type: 'text' },
            { key: '湿重(t)', label: '湿重(t)', type: 'number' },
            { key: '水份(%)', label: '水份(%)', type: 'number' },
            { key: '干重(t)', label: '干重(t)', type: 'number' },
            { key: 'Pb', label: 'Pb品位(%)', type: 'number' },
            { key: 'Zn', label: 'Zn品位(%)', type: 'number' },
            { key: 'Pb^M', label: 'Pb金属量(t)', type: 'number' },
            { key: 'Zn^M', label: 'Zn金属量(t)', type: 'number' },
            { key: '发货单位名称', label: '发货单位名称', type: 'text' },
            { key: '收货单位名称', label: '收货单位名称', type: 'text' },
            { key: '流向', label: '流向', type: 'text' },
            { key: 'created_at', label: '创建时间', type: 'datetime' },
            { key: 'updated_at', label: '更新时间', type: 'datetime' }
          ];
        default:
          return [];
      }
    };

    const fields = getFieldsForDataSource();

    return (
      <div className="space-y-4">
        <div className="text-center pb-2">
          <h3 className="text-lg font-semibold text-primary">{dataSourceTitles[selectedDataSource]}</h3>
          <Badge variant="secondary" className="mt-1">
            {dataSourceTables[selectedDataSource]}
          </Badge>
        </div>

        <Card className="border-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-base">基础信息</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid gap-3">
              {fields.filter(field => field.key !== 'created_at' && field.key !== 'updated_at').map((field) => {
                const value = displayData[field.key];

                // 不可编辑的字段（ID和系统时间字段）
                const isReadOnlyField = field.key === 'id' || field.type === 'datetime';

                if (isEditMode && !isReadOnlyField) {
                  // 编辑模式下的输入组件
                  return (
                    <div key={field.key} className="space-y-2 py-2 border-b border-border/30 last:border-b-0">
                      <Label htmlFor={field.key} className="text-sm font-medium">
                        {field.label}
                      </Label>
                      {field.type === 'number' ? (
                        <Input
                          id={field.key}
                          type="number"
                          step="0.01"
                          value={value || ''}
                          onChange={(e) => handleFieldChange(field.key, parseFloat(e.target.value) || 0)}
                          className="text-sm"
                        />
                      ) : field.type === 'date' ? (
                        <Input
                          id={field.key}
                          type="date"
                          value={value || ''}
                          onChange={(e) => handleFieldChange(field.key, e.target.value)}
                          className="text-sm"
                        />
                      ) : (
                        <Input
                          id={field.key}
                          type="text"
                          value={value || ''}
                          onChange={(e) => handleFieldChange(field.key, e.target.value)}
                          className="text-sm"
                        />
                      )}
                    </div>
                  );
                } else {
                  // 查看模式下的显示
                  let displayValue: string;

                  switch (field.type) {
                    case 'datetime':
                      displayValue = formatDate(value);
                      break;
                    case 'date':
                      displayValue = value || '--';
                      break;
                    case 'number':
                      displayValue = formatValue(value, 2);
                      break;
                    default:
                      displayValue = value || '--';
                  }

                  return (
                    <div key={field.key} className="flex justify-between items-start py-2 border-b border-border/30 last:border-b-0">
                      <span className="font-medium text-sm text-foreground/80 min-w-0 flex-shrink-0 mr-3">
                        {field.label}
                      </span>
                      <span className="text-sm text-right break-all">
                        {displayValue}
                      </span>
                    </div>
                  );
                }
              })}
            </div>
          </CardContent>
        </Card>

        <Card className="border-dashed">
          <CardHeader className="pb-3">
            <CardTitle className="text-base text-muted-foreground">系统信息</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid gap-3">
              {fields.filter(field => field.key === 'created_at' || field.key === 'updated_at').map((field) => {
                const value = displayData[field.key];
                const displayValue = formatDate(value);

                return (
                  <div key={field.key} className="flex justify-between items-start py-2 border-b border-border/30 last:border-b-0">
                    <span className="font-medium text-sm text-muted-foreground min-w-0 flex-shrink-0 mr-3">
                      {field.label}
                    </span>
                    <span className="text-xs text-muted-foreground text-right break-all">
                      {displayValue}
                    </span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }, [rawRowData, selectedRowData, selectedDataSource, formatDate, formatValue, isEditMode, editFormData, handleFieldChange]);

  // 进入编辑模式
  const handleEditMode = useCallback(() => {
    if (rawRowData) {
      setEditFormData({ ...rawRowData });
      setIsEditMode(true);
    }
  }, [rawRowData]);

  // 取消编辑
  const handleCancelEdit = useCallback(() => {
    setIsEditMode(false);
    setEditFormData(null);
  }, []);

  // 保存编辑
  const handleSaveEdit = useCallback(async () => {
    if (!editFormData || !selectedRowData) return;

    setIsSaving(true);
    try {
      const response = await fetch('/api/lab-data/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sampleType: selectedDataSource,
          id: selectedRowData.id,
          data: editFormData
        })
      });

      const result = await response.json();

      if (result.success) {
        // 更新本地数据
        setRawRowData(editFormData);
        setIsEditMode(false);
        setEditFormData(null);

        // 刷新表格数据
        await fetchData();

        console.log('数据保存成功');
      } else {
        console.error('保存失败:', result.error);
        alert('保存失败: ' + result.error);
      }
    } catch (error) {
      console.error('保存数据失败:', error);
      alert('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  }, [editFormData, selectedRowData, selectedDataSource, fetchData]);



  // 行点击处理 - 获取完整数据并打开详情对话框
  const handleRowClick = useCallback(async (item: SampleData) => {
    try {
      // 获取该记录的完整原始数据
      const response = await fetch(`/api/lab-data?sampleType=${selectedDataSource}&limit=100`);
      const result = await response.json();

      if (result.success) {
        // 查找对应的原始数据记录
        const rawData = result.data.find((rawItem: any) => rawItem.id === item.id);
        if (rawData) {
          setSelectedRowData(item);
          setRawRowData(rawData);
          setIsDetailDialogOpen(true);
          // 重置编辑状态
          setIsEditMode(false);
          setEditFormData(null);
        }
      }
    } catch (error) {
      console.error('获取详细数据失败:', error);
    }
  }, [selectedDataSource]);

  // 页面初始化和数据刷新
  useEffect(() => {
    const initializePage = async () => {
      setIsInitialLoading(true);
      try {
        await fetchData();
      } catch (error) {
        console.error('页面初始化失败:', error);
      } finally {
        setIsInitialLoading(false);
      }
    };

    initializePage();
  }, [fetchData]);

  // 监听数据源变化，自动刷新数据
  useEffect(() => {
    if (!isInitialLoading) {
      fetchData();
    }
  }, [selectedDataSource, fetchData, isInitialLoading]);

  // 数据源变化时重新获取数据
  useEffect(() => {
    if (!isInitialLoading) {
      fetchData();
    }
  }, [selectedDataSource, fetchData, isInitialLoading]);

  if (isInitialLoading) {
    return (
      <div className="container mx-auto p-6 space-y-8">
        <div className="space-y-8">
          <Skeleton className="h-8 w-48 mx-auto" />
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <Skeleton key={i} className="h-24" />
            ))}
          </div>
          <Skeleton className="h-64" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* 页面头部 */}
      <div className="relative">
        {/* 汉堡菜单 - 左上角 */}
        <div className="absolute top-0 left-0">
          <HamburgerMenu />
        </div>

        {/* 主题切换按钮 - 右上角 */}
        <div className="absolute top-0 right-0">
          <ThemeToggle />
        </div>

        {/* 页面标题 - 居中 */}
        <div className="text-center mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold mb-2 flex items-center justify-center gap-2">
            <FlaskConical className="h-6 w-6 sm:h-8 sm:w-8" />
            化验室
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground px-4">
            样品化验数据管理与查询系统
          </p>
        </div>
      </div>

      {/* 专项作业区域 */}
      <Card>
        <CardHeader>
          <CardTitle>专项作业区</CardTitle>
          <CardDescription>
            点击选择专项作业区
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {workAreas.map((area) => {
              const IconComponent = area.icon;

              return (
                <Button
                  key={area.dataSource}
                  variant="outline"
                  className="h-auto p-3 sm:p-4 flex flex-col items-center space-y-1 sm:space-y-2 hover:bg-primary/5 hover:border-primary"
                  onClick={() => handleWorkAreaClick(area)}
                >
                  <IconComponent className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
                  <div className="text-center">
                    <h3 className="font-semibold text-xs sm:text-sm">{area.label}</h3>
                    <p className="text-xs text-muted-foreground hidden sm:block">{area.description}</p>
                  </div>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* 化验数据查询区域 */}
      <Card>
        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex-1">
            <CardTitle className="text-lg sm:text-xl">化验数据查询</CardTitle>
            <CardDescription className="text-sm">
              查看 {dataSourceLabel[selectedDataSource]} 的历史化验记录
            </CardDescription>
          </div>
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchData}
              disabled={isLoading}
              className="flex-1 sm:flex-none"
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              刷新数据
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* 数据源切换按钮 */}
          <div className="grid grid-cols-2 sm:flex sm:flex-wrap gap-2 mb-4">
            {(['shift_samples', 'filter_samples', 'incoming_samples', 'outgoing_sample'] as const).map((source) => (
              <Button
                key={source}
                variant={selectedDataSource === source ? "default" : "outline"}
                size="sm"
                onClick={() => handleDataSourceChange(source)}
                className="text-xs sm:text-sm"
              >
                {dataSourceLabel[source]}
              </Button>
            ))}
          </div>

          {/* 日期选择功能 */}
          <div className="mb-6 p-4 bg-muted/30 rounded-lg">
            <h3 className="text-sm font-medium mb-3 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              日期范围选择
            </h3>
            <div className="space-y-4">
              {/* 日期输入 */}
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="flex-1">
                  <label className="text-xs text-muted-foreground mb-1 block">开始日期</label>
                  <Input
                    type="date"
                    value={startDate ? startDate.toISOString().split('T')[0] : ""}
                    onChange={(e) => setStartDate(e.target.value ? new Date(e.target.value) : undefined)}
                    className="w-full"
                  />
                </div>
                <div className="flex-1">
                  <label className="text-xs text-muted-foreground mb-1 block">结束日期</label>
                  <Input
                    type="date"
                    value={endDate ? endDate.toISOString().split('T')[0] : ""}
                    onChange={(e) => setEndDate(e.target.value ? new Date(e.target.value) : undefined)}
                    className="w-full"
                  />
                </div>
              </div>

              {/* 快速选择按钮 */}
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuickDateRange(7)}
                  className="text-xs"
                >
                  最近一周
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuickDateRange(30)}
                  className="text-xs"
                >
                  最近一月
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuickDateRange(180)}
                  className="text-xs"
                >
                  最近半年
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuickDateRange(365)}
                  className="text-xs"
                >
                  最近一年
                </Button>
              </div>
            </div>
          </div>

          {/* 数据表格 */}
          <div className="relative overflow-hidden">
            {isLoading ? (
              <div className="flex items-center justify-center py-10">
                <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : tableData.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-10 text-muted-foreground">
                <Search className="h-12 w-12 mb-4 opacity-50" />
                <p className="text-lg font-medium">暂无 {dataSourceLabel[selectedDataSource]} 数据</p>
                <p className="text-sm mt-2">所选日期范围内没有找到相关记录</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {/* 班样字段顺序：日期，班次，矿物类型，元素，品位(%)，水分(%) */}
                      {selectedDataSource === 'shift_samples' && (
                        <>
                          <TableHead>日期</TableHead>
                          <TableHead>班次</TableHead>
                          <TableHead>矿物类型</TableHead>
                          <TableHead>元素</TableHead>
                          <TableHead>品位(%)</TableHead>
                          <TableHead>水分(%)</TableHead>
                        </>
                      )}

                      {/* 压滤样字段 */}
                      {selectedDataSource === 'filter_samples' && (
                        <>
                          <TableHead>日期</TableHead>
                          <TableHead>元素</TableHead>
                          <TableHead>品位(%)</TableHead>
                          <TableHead>水分(%)</TableHead>
                          <TableHead>操作员</TableHead>
                        </>
                      )}

                      {/* 进厂样字段 */}
                      {selectedDataSource === 'incoming_samples' && (
                        <>
                          <TableHead>日期</TableHead>
                          <TableHead>元素</TableHead>
                          <TableHead>品位(%)</TableHead>
                          <TableHead>水分(%)</TableHead>
                          <TableHead>供应商</TableHead>
                          <TableHead>原矿类型</TableHead>
                        </>
                      )}

                      {/* 出厂样字段：日期，样品编号，元素，品位(%)，水分(%)，采购单位 */}
                      {selectedDataSource === 'outgoing_sample' && (
                        <>
                          <TableHead>日期</TableHead>
                          <TableHead>样品编号</TableHead>
                          <TableHead>元素</TableHead>
                          <TableHead>品位(%)</TableHead>
                          <TableHead>水分(%)</TableHead>
                          <TableHead>采购单位</TableHead>
                        </>
                      )}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tableData.map((item) => (
                      <TableRow
                        key={item.id}
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleRowClick(item)}
                      >
                        {/* 班样数据显示：日期，班次，矿物类型，元素，品位(%)，水分(%) */}
                        {selectedDataSource === 'shift_samples' && (
                          <>
                            <TableCell>{item.record_date}</TableCell>
                            <TableCell>{item.shift}</TableCell>
                            <TableCell>{item.mineral_type}</TableCell>
                            <TableCell>{item.element}</TableCell>
                            <TableCell>{item.grade_value?.toFixed(2) || '-'}</TableCell>
                            <TableCell>{item.moisture_value?.toFixed(2) || '-'}</TableCell>
                          </>
                        )}

                        {/* 压滤样数据显示 */}
                        {selectedDataSource === 'filter_samples' && (
                          <>
                            <TableCell>{item.record_date}</TableCell>
                            <TableCell>{item.element}</TableCell>
                            <TableCell>{item.grade_value?.toFixed(2) || '-'}</TableCell>
                            <TableCell>{item.moisture_value?.toFixed(2) || '-'}</TableCell>
                            <TableCell>{item.operator || '-'}</TableCell>
                          </>
                        )}

                        {/* 进厂样数据显示 */}
                        {selectedDataSource === 'incoming_samples' && (
                          <>
                            <TableCell>{item.record_date}</TableCell>
                            <TableCell>{item.element}</TableCell>
                            <TableCell>{item.grade_value?.toFixed(2) || '-'}</TableCell>
                            <TableCell>{item.moisture_value?.toFixed(2) || '-'}</TableCell>
                            <TableCell>{item.supplier}</TableCell>
                            <TableCell>{item.mineral_type}</TableCell>
                          </>
                        )}

                        {/* 出厂样数据显示：日期，样品编号，元素，品位(%)，水分(%)，采购单位 */}
                        {selectedDataSource === 'outgoing_sample' && (
                          <>
                            <TableCell>{item.shipment_date}</TableCell>
                            <TableCell>{item.sample_number || '-'}</TableCell>
                            <TableCell>{item.element}</TableCell>
                            <TableCell>{item.grade_value?.toFixed(2) || '-'}</TableCell>
                            <TableCell>{item.moisture_value?.toFixed(2) || '-'}</TableCell>
                            <TableCell>{item.purchasing_unit_name}</TableCell>
                          </>
                        )}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 数据对比模块 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            数据对比分析
          </CardTitle>
          <CardDescription>
            金鼎 VS 富鼎翔各环节数据对比
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="incoming" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="incoming">进厂数据</TabsTrigger>
              <TabsTrigger value="production">生产数据</TabsTrigger>
              <TabsTrigger value="outgoing">出厂数据</TabsTrigger>
            </TabsList>

            <TabsContent value="incoming" className="mt-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">进厂原矿数据趋势</h3>
                <Carousel className="w-full">
                  <CarouselContent>
                    <CarouselItem>
                      <ComparisonChart
                        data={chartData.incoming.gradeAndMoisture}
                        title="品位% + 水份% 对比"
                        description="金鼎 VS 富鼎翔进厂原矿品位和水分对比"
                        lines={[
                          { dataKey: "jinding_grade" },
                          { dataKey: "fudingxiang_grade" },
                          { dataKey: "jinding_moisture" },
                          { dataKey: "fudingxiang_moisture" },
                        ]}
                        trendText="品位和水分指标稳定"
                      />
                    </CarouselItem>
                    <CarouselItem>
                      <ComparisonChart
                        data={chartData.incoming.weightAndMetal}
                        title="湿重t + 金属量t 对比"
                        description="金鼎 VS 富鼎翔进厂原矿重量和金属量对比"
                        lines={[
                          { dataKey: "jinding_weight" },
                          { dataKey: "fudingxiang_weight" },
                          { dataKey: "jinding_metal" },
                          { dataKey: "fudingxiang_metal" },
                        ]}
                        trendText="重量和金属量指标正常"
                      />
                    </CarouselItem>
                  </CarouselContent>
                  <CarouselPrevious />
                  <CarouselNext />
                </Carousel>
              </div>
            </TabsContent>

            <TabsContent value="production" className="mt-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">生产过程数据分析</h3>
                <Carousel className="w-full">
                  <CarouselContent>
                    <CarouselItem>
                      <ComparisonChart
                        data={chartData.production.originalOre}
                        title="原矿水份 + 原矿Zn品位 + 原矿Pb品位"
                        description="原矿入选前的关键指标监控"
                        lines={[
                          { dataKey: "moisture" },
                          { dataKey: "zn_grade" },
                          { dataKey: "pb_grade" },
                        ]}
                        trendText="原矿指标稳定在正常范围"
                      />
                    </CarouselItem>
                    <CarouselItem>
                      <ComparisonChart
                        data={chartData.production.concentrate}
                        title="精矿Zn品位 + 精矿Pb品位"
                        description="精矿产品质量指标"
                        lines={[
                          { dataKey: "zn_grade" },
                          { dataKey: "pb_grade" },
                        ]}
                        trendText="精矿品位保持高水平"
                      />
                    </CarouselItem>
                    <CarouselItem>
                      <ComparisonChart
                        data={chartData.production.tailings}
                        title="尾矿Zn品位 + 尾矿Pb品位"
                        description="尾矿损失控制指标"
                        lines={[
                          { dataKey: "zn_grade" },
                          { dataKey: "pb_grade" },
                        ]}
                        trendText="尾矿品位控制良好"
                      />
                    </CarouselItem>
                    <CarouselItem>
                      <ComparisonChart
                        data={chartData.production.recovery}
                        title="回收率"
                        description="金属回收效率指标"
                        lines={[
                          { dataKey: "zn_recovery" },
                          { dataKey: "pb_recovery" },
                        ]}
                        trendText="回收率保持优秀水平"
                      />
                    </CarouselItem>
                  </CarouselContent>
                  <CarouselPrevious />
                  <CarouselNext />
                </Carousel>
              </div>
            </TabsContent>

            <TabsContent value="outgoing" className="mt-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">出厂精矿质量分析</h3>
                <Carousel className="w-full">
                  <CarouselContent>
                    <CarouselItem>
                      <ComparisonChart
                        data={chartData.outgoing.gradeAndMoisture}
                        title="品位% + 水份% 对比"
                        description="金鼎 VS 富鼎翔出厂精矿品位和水分对比"
                        lines={[
                          { dataKey: "jinding_grade" },
                          { dataKey: "fudingxiang_grade" },
                          { dataKey: "jinding_moisture" },
                          { dataKey: "fudingxiang_moisture" },
                        ]}
                        trendText="出厂精矿质量稳定优良"
                      />
                    </CarouselItem>
                    <CarouselItem>
                      <ComparisonChart
                        data={chartData.outgoing.weightAndMetal}
                        title="湿重t + 金属量t 对比"
                        description="金鼎 VS 富鼎翔出厂精矿重量和金属量对比"
                        lines={[
                          { dataKey: "jinding_weight" },
                          { dataKey: "fudingxiang_weight" },
                          { dataKey: "jinding_metal" },
                          { dataKey: "fudingxiang_metal" },
                        ]}
                        trendText="出厂产量保持稳定增长"
                      />
                    </CarouselItem>
                  </CarouselContent>
                  <CarouselPrevious />
                  <CarouselNext />
                </Carousel>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={(open) => {
        setIsDetailDialogOpen(open);
        if (!open) {
          // 关闭对话框时重置编辑状态
          setIsEditMode(false);
          setEditFormData(null);
        }
      }}>
        <DialogContent className="max-w-lg w-[95vw] max-h-[85vh] overflow-y-auto">
          <DialogHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FlaskConical className="h-5 w-5" />
                <DialogTitle>化验数据详情</DialogTitle>
              </div>
              <div className="flex items-center gap-2">
                {isEditMode ? (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSaveEdit}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                    </Button>
                  </>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleEditMode}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    setShowDetailDialog(false);
                    setSelectedRowData(null);
                    setRawRowData(null);
                    setIsEditMode(false);
                    setEditFormData(null);
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <DialogDescription>
              {isEditMode ? '编辑化验记录信息' : '查看完整的化验记录信息'}
            </DialogDescription>
          </DialogHeader>
          {renderDetailContent()}
        </DialogContent>
      </Dialog>

      {/* 统一底部签名 */}
      <Footer />
    </div>
  );
}

export default function LabPage() {
  return (
    <AuthGuard>
      <LabPageContent />
    </AuthGuard>
  );
}
